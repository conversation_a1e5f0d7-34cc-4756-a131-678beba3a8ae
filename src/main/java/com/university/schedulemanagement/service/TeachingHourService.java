package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.*;
import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * TeachingHourService - Service xử lý tổng hợp giờ giảng
 */
public interface TeachingHourService {

    /**
     * Tự động tính toán giờ giảng cho giảng viên trong học kỳ
     */
    void calculateTeachingHours(Long teacherId, Long semesterId);

    /**
     * T<PERSON>h toán tất cả giờ giảng trong học kỳ
     */
    void calculateAllTeachingHours(Long semesterId);

    /**
     * Lấy thống kê giờ giảng theo giảng viên
     */
    TeachingHourResponse getTeachingHoursByTeacher(Long teacherId, Long semesterId);

    /**
     * L<PERSON>y thống kê giờ giảng theo khoa
     */
    List<TeachingHourResponse> getTeachingHoursByDepartment(Long departmentId, Long semesterId);

    /**
     * L<PERSON>y thống kê giờ giảng theo khoảng thời gian (01/8 năm trước đến 31/7 năm sau)
     */
    TeachingHourResponse getTeachingHoursByPeriod(Long teacherId, LocalDate fromDate, LocalDate toDate);

    /**
     * Export báo cáo tổng hợp giờ giảng
     */
    byte[] exportTeachingHoursReport(Long departmentId, Long semesterId);

    /**
     * Lấy báo cáo giờ giảng cá nhân
     */
    TeachingHourResponse getPersonalTeachingHours(Long semesterId);

    /**
     * Lấy top giảng viên theo giờ giảng
     */
    List<TeachingHourResponse> getTopTeachersByHours(Long semesterId, int limit);
}