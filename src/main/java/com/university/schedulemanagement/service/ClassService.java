package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.*;
import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
/**
 * ClassService - Service xử lý lớp học
 */
public interface ClassService {

    LopHoc createClass(ClassRequest request);

    LopHoc updateClass(Long id, ClassRequest request);

    void deleteClass(Long id);

    LopHoc getClassById(Long id);

    Page<LopHoc> getAllClasses(Pageable pageable);

    Page<LopHoc> searchClasses(String keyword, Pageable pageable);

    List<LopHoc> getClassesByMajor(Long majorId);

    List<LopHoc> getClassesByEducationLevel(Long educationLevelId);

    List<LopHoc> getClassesByMajorAndEducationLevel(Long majorId, Long educationLevelId);
}