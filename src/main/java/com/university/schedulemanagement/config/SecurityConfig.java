package com.university.schedulemanagement.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * SecurityConfig - Cấu hình bảo mật
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager(
            AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
                .exceptionHandling()
                .authenticationEntryPoint(jwtAuthenticationEntryPoint)
                .and()
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeRequests(authz -> authz
                        // Public endpoints
                        .antMatchers("/api/auth/login").permitAll()
                        .antMatchers("/api/auth/reset-password").permitAll()
                        .antMatchers("/api/health").permitAll()

                        // Swagger endpoints
                        .antMatchers("/api/api-docs/**").permitAll()
                        .antMatchers("/api/swagger-ui/**").permitAll()
                        .antMatchers("/api/swagger-ui.html").permitAll()

                        // Static resources
                        .antMatchers("/api/static/**").permitAll()

                        // Admin only endpoints
                        .antMatchers(HttpMethod.POST, "/api/teachers").hasRole("ADMIN")
                        .antMatchers(HttpMethod.PUT, "/api/teachers/**").hasRole("ADMIN")
                        .antMatchers(HttpMethod.DELETE, "/api/teachers/**").hasRole("ADMIN")
                        .antMatchers(HttpMethod.POST, "/api/subjects").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.PUT, "/api/subjects/**").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.DELETE, "/api/subjects/**").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.POST, "/api/classes").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.PUT, "/api/classes/**").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.DELETE, "/api/classes/**").hasAnyRole("ADMIN", "TRUONG_KHOA")

                        // Schedule management
                        .antMatchers(HttpMethod.POST, "/api/schedules").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.PUT, "/api/schedules/**").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.DELETE, "/api/schedules/**").hasAnyRole("ADMIN", "TRUONG_KHOA")

                        // Teaching hours - Admins and Department heads can view all, teachers can view their own
                        .antMatchers(HttpMethod.GET, "/api/teaching-hours/personal").hasRole("GIANG_VIEN")
                        .antMatchers(HttpMethod.GET, "/api/teaching-hours/**").hasAnyRole("ADMIN", "TRUONG_KHOA", "GIANG_VIEN")

                        // Export functions
                        .antMatchers("/api/export/personal/**").hasRole("GIANG_VIEN")
                        .antMatchers("/api/export/**").hasAnyRole("ADMIN", "TRUONG_KHOA")

                        // All other requests need authentication
                        .anyRequest().authenticated()
                );

        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
