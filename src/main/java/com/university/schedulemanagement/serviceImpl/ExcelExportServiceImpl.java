package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.constant.AppConstants;
import com.university.schedulemanagement.dto.response.ScheduleResponse;
import com.university.schedulemanagement.dto.response.TeachingHourResponse;
import com.university.schedulemanagement.entity.*;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.service.ExcelExportService;
import com.university.schedulemanagement.service.ScheduleService;
import com.university.schedulemanagement.service.TeachingHourService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ExcelExportServiceImpl - Implementation của ExcelExportService
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExcelExportServiceImpl implements ExcelExportService {

    private final ScheduleService scheduleService;
    private final TeachingHourService teachingHourService;

    @Override
    public byte[] exportPersonalSchedule(Long teacherId, Long semesterId) {
        log.info("Exporting personal schedule for teacher: {}, semester: {}", teacherId, semesterId);

        try (Workbook workbook = new XSSFWorkbook()) {
            // Lấy dữ liệu
            List<ScheduleResponse> schedules = scheduleService.getSchedulesByTeacher(teacherId, semesterId);

            if (schedules.isEmpty()) {
                throw new BadRequestException("Không có dữ liệu lịch giảng để xuất");
            }

            // Tạo sheet
            Sheet sheet = workbook.createSheet("Lịch Giảng Cá Nhân");

            // Tạo styles
            Map<String, CellStyle> styles = createStyles(workbook);

            // Tạo header
            createPersonalScheduleHeader(sheet, styles, schedules.get(0));

            // Tạo table header
            createScheduleTableHeader(sheet, styles, 4);

            // Fill data
            fillScheduleData(sheet, styles, schedules, 5);

            // Auto size columns
            autoSizeColumns(sheet, 9);

            // Convert to byte array
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("Personal schedule exported successfully for teacher: {}", teacherId);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("Failed to export personal schedule for teacher: {}", teacherId, e);
            throw new RuntimeException("Lỗi khi xuất file Excel: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportClassSchedule(Long classId, Long semesterId) {
        log.info("Exporting class schedule for class: {}, semester: {}", classId, semesterId);

        try (Workbook workbook = new XSSFWorkbook()) {
            // Lấy dữ liệu
            List<ScheduleResponse> schedules = scheduleService.getSchedulesByClass(classId, semesterId);

            if (schedules.isEmpty()) {
                throw new BadRequestException("Không có dữ liệu lịch học để xuất");
            }

            // Tạo sheet
            Sheet sheet = workbook.createSheet("Lịch Học Lớp");

            // Tạo styles
            Map<String, CellStyle> styles = createStyles(workbook);

            // Tạo header
            createClassScheduleHeader(sheet, styles, schedules.get(0));

            // Tạo table header
            createScheduleTableHeader(sheet, styles, 4);

            // Fill data
            fillScheduleData(sheet, styles, schedules, 5);

            // Auto size columns
            autoSizeColumns(sheet, 9);

            // Convert to byte array
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("Class schedule exported successfully for class: {}", classId);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("Failed to export class schedule for class: {}", classId, e);
            throw new RuntimeException("Lỗi khi xuất file Excel: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportTeachingHoursReport(Long departmentId, Long semesterId) {
        log.info("Exporting teaching hours report for department: {}, semester: {}", departmentId, semesterId);

        try (Workbook workbook = new XSSFWorkbook()) {
            // Lấy dữ liệu
            List<TeachingHourResponse> teachingHours = teachingHourService.getTeachingHoursByDepartment(departmentId, semesterId);

            if (teachingHours.isEmpty()) {
                throw new BadRequestException("Không có dữ liệu giờ giảng để xuất");
            }

            // Tạo sheet tổng hợp
            Sheet summarySheet = workbook.createSheet("Tổng Hợp Giờ Giảng");

            // Tạo styles
            Map<String, CellStyle> styles = createStyles(workbook);

            // Tạo header
            createTeachingHoursReportHeader(summarySheet, styles, teachingHours.get(0));

            // Tạo table header
            createTeachingHoursTableHeader(summarySheet, styles, 4);

            // Fill data
            fillTeachingHoursData(summarySheet, styles, teachingHours, 5);

            // Tạo sheet chi tiết cho từng giảng viên
            for (TeachingHourResponse teacher : teachingHours) {
                createTeacherDetailSheet(workbook, styles, teacher);
            }

            // Auto size columns
            autoSizeColumns(summarySheet, 8);

            // Convert to byte array
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("Teaching hours report exported successfully for department: {}", departmentId);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("Failed to export teaching hours report for department: {}", departmentId, e);
            throw new RuntimeException("Lỗi khi xuất file Excel: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportTeacherList(Long departmentId) {
        log.info("Exporting teacher list for department: {}", departmentId);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Danh Sách Giảng Viên");
            Map<String, CellStyle> styles = createStyles(workbook);

            // TODO: Implement teacher list export
            // Cần thêm service để lấy danh sách giảng viên

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("Failed to export teacher list for department: {}", departmentId, e);
            throw new RuntimeException("Lỗi khi xuất file Excel: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportSubjectList(Long departmentId) {
        log.info("Exporting subject list for department: {}", departmentId);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Danh Sách Môn Học");
            Map<String, CellStyle> styles = createStyles(workbook);

            // TODO: Implement subject list export
            // Cần thêm service để lấy danh sách môn học

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("Failed to export subject list for department: {}", departmentId, e);
            throw new RuntimeException("Lỗi khi xuất file Excel: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportFullSchedule(Long semesterId) {
        log.info("Exporting full schedule for semester: {}", semesterId);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Lịch Giảng Toàn Trường");
            Map<String, CellStyle> styles = createStyles(workbook);

            // TODO: Implement full schedule export
            // Cần lấy tất cả lịch giảng trong học kỳ

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("Failed to export full schedule for semester: {}", semesterId, e);
            throw new RuntimeException("Lỗi khi xuất file Excel: " + e.getMessage());
        }
    }

    // ==================== PRIVATE HELPER METHODS ====================

    private Map<String, CellStyle> createStyles(Workbook workbook) {
        Map<String, CellStyle> styles = new java.util.HashMap<>();

        // Title style
        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        styles.put("title", titleStyle);

        // Header style
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        styles.put("header", headerStyle);

        // Data style
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        styles.put("data", dataStyle);

        // Number style
        CellStyle numberStyle = workbook.createCellStyle();
        numberStyle.cloneStyleFrom(dataStyle);
        numberStyle.setAlignment(HorizontalAlignment.RIGHT);
        DataFormat format = workbook.createDataFormat();
        numberStyle.setDataFormat(format.getFormat("#,##0.00"));
        styles.put("number", numberStyle);

        // Center style
        CellStyle centerStyle = workbook.createCellStyle();
        centerStyle.cloneStyleFrom(dataStyle);
        centerStyle.setAlignment(HorizontalAlignment.CENTER);
        styles.put("center", centerStyle);

        return styles;
    }

    private void createPersonalScheduleHeader(Sheet sheet, Map<String, CellStyle> styles, ScheduleResponse sampleSchedule) {
        // Title
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("LỊCH GIẢNG CÁ NHÂN");
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));

        // Info
        Row infoRow1 = sheet.createRow(1);
        infoRow1.createCell(0).setCellValue("Giảng viên: " + sampleSchedule.getTenGiangVien());

        Row infoRow2 = sheet.createRow(2);
        infoRow2.createCell(0).setCellValue("Ngày xuất: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern(AppConstants.DATETIME_FORMAT)));
    }

    private void createClassScheduleHeader(Sheet sheet, Map<String, CellStyle> styles, ScheduleResponse sampleSchedule) {
        // Title
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("LỊCH HỌC LỚP");
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));

        // Info
        Row infoRow1 = sheet.createRow(1);
        infoRow1.createCell(0).setCellValue("Lớp: " + sampleSchedule.getTenLop());

        Row infoRow2 = sheet.createRow(2);
        infoRow2.createCell(0).setCellValue("Ngày xuất: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern(AppConstants.DATETIME_FORMAT)));
    }

    private void createScheduleTableHeader(Sheet sheet, Map<String, CellStyle> styles, int startRow) {
        Row headerRow = sheet.createRow(startRow);

        String[] headers = {
                "STT", "Môn học", "Lớp", "Giảng viên", "Thứ", "Buổi", "Phòng", "Số tiết", "Hệ số"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(styles.get("header"));
        }
    }

    private void fillScheduleData(Sheet sheet, Map<String, CellStyle> styles, List<ScheduleResponse> schedules, int startRow) {
        int rowNum = startRow;
        int stt = 1;

        for (ScheduleResponse schedule : schedules) {
            Row row = sheet.createRow(rowNum++);

            // STT
            Cell sttCell = row.createCell(0);
            sttCell.setCellValue(stt++);
            sttCell.setCellStyle(styles.get("center"));

            // Môn học
            Cell subjectCell = row.createCell(1);
            subjectCell.setCellValue(schedule.getMaMonHoc() + " - " + schedule.getTenMonHoc());
            subjectCell.setCellStyle(styles.get("data"));

            // Lớp
            Cell classCell = row.createCell(2);
            classCell.setCellValue(schedule.getMaLop() + " - " + schedule.getTenLop());
            classCell.setCellStyle(styles.get("data"));

            // Giảng viên
            Cell teacherCell = row.createCell(3);
            teacherCell.setCellValue(schedule.getTenGiangVien());
            teacherCell.setCellStyle(styles.get("data"));

            // Thứ
            Cell dayCell = row.createCell(4);
            dayCell.setCellValue(schedule.getThuHocText());
            dayCell.setCellStyle(styles.get("center"));

            // Buổi
            Cell sessionCell = row.createCell(5);
            sessionCell.setCellValue(schedule.getTenBuoi() + " (" +
                    schedule.getGioBatDau() + "-" + schedule.getGioKetThuc() + ")");
            sessionCell.setCellStyle(styles.get("center"));

            // Phòng
            Cell roomCell = row.createCell(6);
            roomCell.setCellValue(schedule.getTenPhong() + " (" + schedule.getTenCoSo() + ")");
            roomCell.setCellStyle(styles.get("center"));

            // Số tiết
            Cell periodsCell = row.createCell(7);
            periodsCell.setCellValue(schedule.getSoTiet());
            periodsCell.setCellStyle(styles.get("number"));

            // Hệ số
            Cell coefficientCell = row.createCell(8);
            coefficientCell.setCellValue(schedule.getHeSo().doubleValue());
            coefficientCell.setCellStyle(styles.get("number"));
        }

        // Tổng cộng
        Row totalRow = sheet.createRow(rowNum + 1);
        Cell totalLabelCell = totalRow.createCell(6);
        totalLabelCell.setCellValue("TỔNG CỘNG:");
        totalLabelCell.setCellStyle(styles.get("header"));

        // Tổng số tiết
        int totalPeriods = schedules.stream().mapToInt(ScheduleResponse::getSoTiet).sum();
        Cell totalPeriodsCell = totalRow.createCell(7);
        totalPeriodsCell.setCellValue(totalPeriods);
        totalPeriodsCell.setCellStyle(styles.get("number"));

        // Tổng giờ quy đổi
        double totalWeightedHours = schedules.stream()
                .mapToDouble(s -> s.getSoGioQuyDoi().doubleValue())
                .sum();
        Cell totalWeightedCell = totalRow.createCell(8);
        totalWeightedCell.setCellValue(totalWeightedHours);
        totalWeightedCell.setCellStyle(styles.get("number"));
    }

    private void createTeachingHoursReportHeader(Sheet sheet, Map<String, CellStyle> styles, TeachingHourResponse sampleData) {
        // Title
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("BÁO CÁO TỔNG HỢP GIỜ GIẢNG");
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 7));

        // Info
        Row infoRow1 = sheet.createRow(1);
        infoRow1.createCell(0).setCellValue("Khoa: " + sampleData.getTenKhoa());

        Row infoRow2 = sheet.createRow(2);
        infoRow2.createCell(0).setCellValue("Học kỳ: " + sampleData.getTenHocKy());
        infoRow2.createCell(4).setCellValue("Ngày xuất: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern(AppConstants.DATETIME_FORMAT)));
    }

    private void createTeachingHoursTableHeader(Sheet sheet, Map<String, CellStyle> styles, int startRow) {
        Row headerRow = sheet.createRow(startRow);

        String[] headers = {
                "STT", "Mã GV", "Tên giảng viên", "Giờ LT", "Giờ TH", "Tổng giờ", "Giờ quy đổi", "Tỷ lệ LT/TH"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(styles.get("header"));
        }
    }

    private void fillTeachingHoursData(Sheet sheet, Map<String, CellStyle> styles,
                                       List<TeachingHourResponse> teachingHours, int startRow) {
        int rowNum = startRow;
        int stt = 1;

        for (TeachingHourResponse teachingHour : teachingHours) {
            Row row = sheet.createRow(rowNum++);

            // STT
            Cell sttCell = row.createCell(0);
            sttCell.setCellValue(stt++);
            sttCell.setCellStyle(styles.get("center"));

            // Mã GV
            Cell codeCell = row.createCell(1);
            codeCell.setCellValue(teachingHour.getMaCanBo());
            codeCell.setCellStyle(styles.get("center"));

            // Tên giảng viên
            Cell nameCell = row.createCell(2);
            nameCell.setCellValue(teachingHour.getTenCanBo());
            nameCell.setCellStyle(styles.get("data"));

            // Giờ LT
            Cell ltHoursCell = row.createCell(3);
            ltHoursCell.setCellValue(teachingHour.getTongGioLt().doubleValue());
            ltHoursCell.setCellStyle(styles.get("number"));

            // Giờ TH
            Cell thHoursCell = row.createCell(4);
            thHoursCell.setCellValue(teachingHour.getTongGioTh().doubleValue());
            thHoursCell.setCellStyle(styles.get("number"));

            // Tổng giờ
            Cell totalHoursCell = row.createCell(5);
            totalHoursCell.setCellValue(teachingHour.getTongGioTong().doubleValue());
            totalHoursCell.setCellStyle(styles.get("number"));

            // Giờ quy đổi
            Cell weightedHoursCell = row.createCell(6);
            weightedHoursCell.setCellValue(teachingHour.getTongGioQuyDoi().doubleValue());
            weightedHoursCell.setCellStyle(styles.get("number"));

            // Tỷ lệ LT/TH
            Cell ratioCell = row.createCell(7);
            ratioCell.setCellValue(teachingHour.getTyLePhanBo());
            ratioCell.setCellStyle(styles.get("center"));
        }

        // Tổng cộng
        Row totalRow = sheet.createRow(rowNum + 1);
        Cell totalLabelCell = totalRow.createCell(2);
        totalLabelCell.setCellValue("TỔNG CỘNG:");
        totalLabelCell.setCellStyle(styles.get("header"));

        // Tính tổng các cột số
        double totalLt = teachingHours.stream().mapToDouble(t -> t.getTongGioLt().doubleValue()).sum();
        double totalTh = teachingHours.stream().mapToDouble(t -> t.getTongGioTh().doubleValue()).sum();
        double totalHours = teachingHours.stream().mapToDouble(t -> t.getTongGioTong().doubleValue()).sum();
        double totalWeighted = teachingHours.stream().mapToDouble(t -> t.getTongGioQuyDoi().doubleValue()).sum();

        totalRow.createCell(3).setCellValue(totalLt);
        totalRow.createCell(4).setCellValue(totalTh);
        totalRow.createCell(5).setCellValue(totalHours);
        totalRow.createCell(6).setCellValue(totalWeighted);

        // Apply styles to total row
        for (int i = 3; i <= 6; i++) {
            totalRow.getCell(i).setCellStyle(styles.get("number"));
        }
    }

    private void createTeacherDetailSheet(Workbook workbook, Map<String, CellStyle> styles, TeachingHourResponse teacher) {
        // Tạo sheet chi tiết cho giảng viên
        String sheetName = teacher.getMaCanBo() + " - " + teacher.getTenCanBo();
        // Giới hạn độ dài tên sheet
        if (sheetName.length() > 31) {
            sheetName = sheetName.substring(0, 31);
        }

        Sheet detailSheet = workbook.createSheet(sheetName);

        // Header
        Row titleRow = detailSheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("CHI TIẾT GIỜ GIẢNG - " + teacher.getTenCanBo());
        titleCell.setCellStyle(styles.get("title"));
        detailSheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));

        // Thông tin tổng hợp
        Row summaryRow1 = detailSheet.createRow(2);
        summaryRow1.createCell(0).setCellValue("Tổng giờ LT:");
        summaryRow1.createCell(1).setCellValue(teacher.getTongGioLt().doubleValue());
        summaryRow1.createCell(3).setCellValue("Tổng giờ TH:");
        summaryRow1.createCell(4).setCellValue(teacher.getTongGioTh().doubleValue());

        Row summaryRow2 = detailSheet.createRow(3);
        summaryRow2.createCell(0).setCellValue("Tổng giờ:");
        summaryRow2.createCell(1).setCellValue(teacher.getTongGioTong().doubleValue());
        summaryRow2.createCell(3).setCellValue("Giờ quy đổi:");
        summaryRow2.createCell(4).setCellValue(teacher.getTongGioQuyDoi().doubleValue());

        // Nếu có chi tiết lịch giảng
        if (teacher.getLichGiangList() != null && !teacher.getLichGiangList().isEmpty()) {
            createScheduleTableHeader(detailSheet, styles, 5);
            fillScheduleData(detailSheet, styles, teacher.getLichGiangList(), 6);
        }

        // Auto size columns
        autoSizeColumns(detailSheet, 9);
    }

    private void autoSizeColumns(Sheet sheet, int numColumns) {
        for (int i = 0; i < numColumns; i++) {
            sheet.autoSizeColumn(i);
            // Đặt độ rộng tối thiểu
            if (sheet.getColumnWidth(i) < 2000) {
                sheet.setColumnWidth(i, 2000);
            }
            // Đặt độ rộng tối đa
            if (sheet.getColumnWidth(i) > 8000) {
                sheet.setColumnWidth(i, 8000);
            }
        }
    }

    /**
     * Xuất lịch giảng theo tuần
     */
    public byte[] exportWeeklySchedule(Long teacherId, Long semesterId, int weekNumber) {
        log.info("Exporting weekly schedule for teacher: {}, semester: {}, week: {}", teacherId, semesterId, weekNumber);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Lịch Giảng Tuần " + weekNumber);
            Map<String, CellStyle> styles = createStyles(workbook);

            // Lấy dữ liệu lịch giảng tuần
            List<ScheduleResponse> weeklySchedules = ((ScheduleServiceImpl) scheduleService)
                    .getWeeklySchedule(teacherId, semesterId, weekNumber);

            if (weeklySchedules.isEmpty()) {
                throw new BadRequestException("Không có lịch giảng trong tuần " + weekNumber);
            }

            // Tạo tiêu đề
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("LỊCH GIẢNG TUẦN " + weekNumber);
            titleCell.setCellStyle(styles.get("title"));
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

            // Tạo lịch theo dạng bảng tuần
            createWeeklyScheduleTable(sheet, styles, weeklySchedules, 3);

            autoSizeColumns(sheet, 7);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("Failed to export weekly schedule", e);
            throw new RuntimeException("Lỗi khi xuất lịch giảng tuần: " + e.getMessage());
        }
    }

    private void createWeeklyScheduleTable(Sheet sheet, Map<String, CellStyle> styles,
                                           List<ScheduleResponse> schedules, int startRow) {
        // Tạo header cho các ngày trong tuần
        Row headerRow = sheet.createRow(startRow);
        String[] dayHeaders = {"Buổi", "Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7", "CN"};

        for (int i = 0; i < dayHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(dayHeaders[i]);
            cell.setCellStyle(styles.get("header"));
        }

        // Nhóm lịch theo buổi và thứ
        Map<String, Map<Integer, List<ScheduleResponse>>> scheduleMatrix = schedules.stream()
                .collect(Collectors.groupingBy(
                        ScheduleResponse::getTenBuoi,
                        Collectors.groupingBy(ScheduleResponse::getThuHoc)
                ));

        // Tạo các hàng cho từng buổi
        String[] sessions = {"Sáng", "Chiều", "Tối"};
        int currentRow = startRow + 1;

        for (String session : sessions) {
            Row sessionRow = sheet.createRow(currentRow++);

            // Cột buổi học
            Cell sessionCell = sessionRow.createCell(0);
            sessionCell.setCellValue(session);
            sessionCell.setCellStyle(styles.get("header"));

            // Các cột thứ 2-8
            for (int day = 2; day <= 8; day++) {
                Cell dayCell = sessionRow.createCell(day - 1);

                Map<Integer, List<ScheduleResponse>> daySchedules = scheduleMatrix.get(session);
                if (daySchedules != null && daySchedules.containsKey(day)) {
                    List<ScheduleResponse> dayScheduleList = daySchedules.get(day);
                    StringBuilder cellContent = new StringBuilder();

                    for (ScheduleResponse schedule : dayScheduleList) {
                        if (cellContent.length() > 0) {
                            cellContent.append("\n");
                        }
                        cellContent.append(schedule.getTenMonHoc())
                                .append("\n")
                                .append(schedule.getTenPhong())
                                .append(" (")
                                .append(schedule.getSoTiet())
                                .append(" tiết)");
                    }

                    dayCell.setCellValue(cellContent.toString());
                } else {
                    dayCell.setCellValue("");
                }

                dayCell.setCellStyle(styles.get("data"));
            }
        }

        // Điều chỉnh chiều cao hàng để hiển thị đầy đủ nội dung
        for (int i = startRow + 1; i < currentRow; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                row.setHeightInPoints(60); // Tăng chiều cao hàng
            }
        }
    }

    /**
     * Xuất báo cáo so sánh giờ giảng
     */
    public byte[] exportComparisonReport(Long departmentId, Long semesterId) {
        log.info("Exporting comparison report for department: {}, semester: {}", departmentId, semesterId);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("So Sánh Giờ Giảng");
            Map<String, CellStyle> styles = createStyles(workbook);

            // Tạm thời sử dụng empty list, có thể implement chi tiết sau
            List<TeachingHourResponse> teachingHours = Collections.emptyList();

            // Tạo biểu đồ so sánh (dạng bảng)
            createComparisonChart(sheet, styles, teachingHours);

            autoSizeColumns(sheet, 10);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("Failed to export comparison report", e);
            throw new RuntimeException("Lỗi khi xuất báo cáo so sánh: " + e.getMessage());
        }
    }

    private void createComparisonChart(Sheet sheet, Map<String, CellStyle> styles,
                                       List<TeachingHourResponse> teachingHours) {
        // Title
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("BIỂU ĐỒ SO SÁNH GIỜ GIẢNG");
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 9));

        // Tính trung bình
        double avgHours = teachingHours.stream()
                .mapToDouble(t -> t.getTongGioQuyDoi().doubleValue())
                .average()
                .orElse(0.0);

        Row avgRow = sheet.createRow(2);
        avgRow.createCell(0).setCellValue("Trung bình khoa: " + String.format("%.2f", avgHours) + " giờ");

        // Header bảng so sánh
        Row headerRow = sheet.createRow(4);
        String[] headers = {
                "STT", "Giảng viên", "Giờ quy đổi", "So với TB", "Xếp hạng",
                "Giờ LT", "Giờ TH", "Tỷ lệ", "Đánh giá", "Ghi chú"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(styles.get("header"));
        }

        // Fill data
        int rowNum = 5;
        int rank = 1;

        for (TeachingHourResponse teacher : teachingHours) {
            Row row = sheet.createRow(rowNum++);

            double teacherHours = teacher.getTongGioQuyDoi().doubleValue();
            double diff = teacherHours - avgHours;

            row.createCell(0).setCellValue(rank++);
            row.createCell(1).setCellValue(teacher.getTenCanBo());
            row.createCell(2).setCellValue(teacherHours);
            row.createCell(3).setCellValue(String.format("%+.2f", diff));
            row.createCell(4).setCellValue(rank - 1);
            row.createCell(5).setCellValue(teacher.getTongGioLt().doubleValue());
            row.createCell(6).setCellValue(teacher.getTongGioTh().doubleValue());
            row.createCell(7).setCellValue(teacher.getTyLePhanBo());

            // Đánh giá
            String evaluation;
            if (diff > 20) {
                evaluation = "Cao";
            } else if (diff > 0) {
                evaluation = "Trên TB";
            } else if (diff > -20) {
                evaluation = "Dưới TB";
            } else {
                evaluation = "Thấp";
            }
            row.createCell(8).setCellValue(evaluation);

            // Apply styles
            for (int i = 0; i < 10; i++) {
                Cell cell = row.getCell(i);
                if (cell == null) cell = row.createCell(i);

                if (i == 2 || i == 5 || i == 6) {
                    cell.setCellStyle(styles.get("number"));
                } else {
                    cell.setCellStyle(styles.get("data"));
                }
            }
        }
    }
}
