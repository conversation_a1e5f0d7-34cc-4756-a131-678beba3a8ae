package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.request.SubjectRequest;
import com.university.schedulemanagement.dto.request.ClassRequest;
import com.university.schedulemanagement.dto.request.TeacherRequest;
import com.university.schedulemanagement.entity.*;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.repository.*;
import com.university.schedulemanagement.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * SubjectServiceImpl - Implementation của SubjectService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SubjectServiceImpl implements SubjectService {

    private final MonHocRepository monHocRepository;
    private final PBMMRepository pbmmRepository;
    private final HeDaoTaoRepository heDaoTaoRepository;
    private final LoaiMonHocRepository loaiMonHocRepository;
    private final NhomLKRepository nhomLKRepository;
    private final AuthService authService;
    private final ModelMapper modelMapper;

    @Override
    public MonHoc createSubject(SubjectRequest request) {
        log.info("Creating subject: {}", request.getMaMonHoc());

        // Kiểm tra quyền
        if (!authService.isAdmin() && !authService.isTruongKhoa()) {
            throw new BadRequestException("Bạn không có quyền tạo môn học");
        }

        // Kiểm tra trùng mã môn học
        if (monHocRepository.findByMaMonHoc(request.getMaMonHoc()).isPresent()) {
            throw new BadRequestException("Mã môn học đã tồn tại: " + request.getMaMonHoc());
        }

        // Validate related entities
        validateSubjectRelatedEntities(request);

        // Kiểm tra quyền tạo môn học cho khoa
        if (authService.isTruongKhoa() &&
                !authService.canAccessDepartmentData(request.getIdPbmm())) {
            throw new BadRequestException("Bạn chỉ có thể tạo môn học cho khoa của mình");
        }

        // Tạo entity
        MonHoc monHoc = new MonHoc();
        mapSubjectRequestToEntity(request, monHoc);

        monHoc = monHocRepository.save(monHoc);
        log.info("Subject created successfully: {}", monHoc.getMaMonHoc());

        return monHoc;
    }

    @Override
    public MonHoc updateSubject(Long id, SubjectRequest request) {
        log.info("Updating subject ID: {}", id);

        MonHoc existingSubject = monHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học với ID: " + id));

        // Kiểm tra quyền
        if (!authService.isAdmin() &&
                (!authService.isTruongKhoa() ||
                        !authService.canAccessDepartmentData(existingSubject.getIdPbmm()))) {
            throw new BadRequestException("Bạn không có quyền sửa môn học này");
        }

        // Kiểm tra trùng mã môn học (nếu thay đổi)
        if (!existingSubject.getMaMonHoc().equals(request.getMaMonHoc())) {
            if (monHocRepository.findByMaMonHoc(request.getMaMonHoc()).isPresent()) {
                throw new BadRequestException("Mã môn học đã tồn tại: " + request.getMaMonHoc());
            }
        }

        // Validate related entities
        validateSubjectRelatedEntities(request);

        // Update entity
        mapSubjectRequestToEntity(request, existingSubject);
        existingSubject = monHocRepository.save(existingSubject);

        log.info("Subject updated successfully: {}", existingSubject.getMaMonHoc());
        return existingSubject;
    }

    @Override
    public void deleteSubject(Long id) {
        log.info("Deleting subject ID: {}", id);

        MonHoc subject = monHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học với ID: " + id));

        // Kiểm tra quyền
        if (!authService.isAdmin() &&
                (!authService.isTruongKhoa() ||
                        !authService.canAccessDepartmentData(subject.getIdPbmm()))) {
            throw new BadRequestException("Bạn không có quyền xóa môn học này");
        }

        // Kiểm tra môn học có đang được sử dụng không
        if (subject.getLichGiangList() != null && !subject.getLichGiangList().isEmpty()) {
            throw new BadRequestException("Không thể xóa môn học đang có lịch giảng");
        }

        monHocRepository.delete(subject);
        log.info("Subject deleted successfully: {}", subject.getMaMonHoc());
    }

    @Override
    @Transactional(readOnly = true)
    public MonHoc getSubjectById(Long id) {
        return monHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học với ID: " + id));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MonHoc> getAllSubjects(Pageable pageable) {
        return monHocRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MonHoc> searchSubjects(String keyword, Pageable pageable) {
        return monHocRepository.findByKeyword(keyword, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MonHoc> getSubjectsByDepartment(Long departmentId) {
        return monHocRepository.findByIdPbmm(departmentId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MonHoc> getSubjectsByEducationLevel(Long educationLevelId) {
        return monHocRepository.findByIdHeDaoTao(educationLevelId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MonHoc> getSubjectsByDepartmentAndEducationLevel(Long departmentId, Long educationLevelId) {
        return monHocRepository.findByKhoaAndHeDaoTao(departmentId, educationLevelId);
    }
}
