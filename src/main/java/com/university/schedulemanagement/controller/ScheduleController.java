package com.university.schedulemanagement.controller;

import com.university.schedulemanagement.dto.request.LoginRequest;
import com.university.schedulemanagement.dto.request.ChangePasswordRequest;
import com.university.schedulemanagement.dto.request.ScheduleRequest;
import com.university.schedulemanagement.dto.response.ApiResponse;
import com.university.schedulemanagement.dto.response.LoginResponse;
import com.university.schedulemanagement.dto.response.ScheduleResponse;
import com.university.schedulemanagement.service.AuthService;
import com.university.schedulemanagement.service.ScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ScheduleController - Controller xử lý lịch giảng
 */
@RestController
@RequestMapping("/schedules")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Schedule Management", description = "API quản lý lịch giảng")
public class ScheduleController {

    private final ScheduleService scheduleService;

    @PostMapping
    @Operation(summary = "Tạo lịch giảng", description = "Tạo lịch giảng mới theo quy trình 7 bước")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<ScheduleResponse>> createSchedule(@Valid @RequestBody ScheduleRequest request) {
        try {
            log.info("Creating schedule for teacher: {}, subject: {}", request.getIdCanBo(), request.getIdMonHoc());

            // Kiểm tra xung đột trước khi tạo
            if (scheduleService.checkScheduleConflict(request)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Có xung đột lịch giảng. Vui lòng kiểm tra lại thời gian, phòng học hoặc giảng viên."));
            }

            ScheduleResponse response = scheduleService.createSchedule(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Tạo lịch giảng thành công"));
        } catch (Exception e) {
            log.error("Create schedule failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Tạo lịch giảng thất bại: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Cập nhật lịch giảng", description = "Cập nhật thông tin lịch giảng")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<ScheduleResponse>> updateSchedule(
            @PathVariable Long id,
            @Valid @RequestBody ScheduleRequest request) {
        try {
            log.info("Updating schedule ID: {}", id);
            ScheduleResponse response = scheduleService.updateSchedule(id, request);
            return ResponseEntity.ok(ApiResponse.success(response, "Cập nhật lịch giảng thành công"));
        } catch (Exception e) {
            log.error("Update schedule failed for ID: {}, error: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Cập nhật lịch giảng thất bại: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Xóa lịch giảng", description = "Xóa lịch giảng theo ID")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<String>> deleteSchedule(@PathVariable Long id) {
        try {
            log.info("Deleting schedule ID: {}", id);
            scheduleService.deleteSchedule(id);
            return ResponseEntity.ok(ApiResponse.success("Xóa lịch giảng thành công"));
        } catch (Exception e) {
            log.error("Delete schedule failed for ID: {}, error: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Xóa lịch giảng thất bại: " + e.getMessage()));
        }
    }

    @GetMapping("/teacher/{teacherId}")
    @Operation(summary = "Lịch giảng theo giảng viên", description = "Lấy lịch giảng của một giảng viên")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<List<ScheduleResponse>>> getSchedulesByTeacher(
            @PathVariable Long teacherId,
            @RequestParam(required = false) Long semesterId) {
        try {
            log.info("Getting schedules for teacher: {}, semester: {}", teacherId, semesterId);
            List<ScheduleResponse> schedules = scheduleService.getSchedulesByTeacher(teacherId, semesterId);
            return ResponseEntity.ok(ApiResponse.success(schedules));
        } catch (Exception e) {
            log.error("Get schedules by teacher failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lấy lịch giảng thất bại: " + e.getMessage()));
        }
    }

    @GetMapping("/class/{classId}")
    @Operation(summary = "Lịch giảng theo lớp", description = "Lấy lịch giảng của một lớp học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<List<ScheduleResponse>>> getSchedulesByClass(
            @PathVariable Long classId,
            @RequestParam(required = false) Long semesterId) {
        try {
            log.info("Getting schedules for class: {}, semester: {}", classId, semesterId);
            List<ScheduleResponse> schedules = scheduleService.getSchedulesByClass(classId, semesterId);
            return ResponseEntity.ok(ApiResponse.success(schedules));
        } catch (Exception e) {
            log.error("Get schedules by class failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lấy lịch giảng thất bại: " + e.getMessage()));
        }
    }

    @GetMapping("/semester/{semesterId}")
    @Operation(summary = "Lịch giảng theo học kỳ", description = "Lấy tất cả lịch giảng trong học kỳ")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<PageResponse<ScheduleResponse>>> getSchedulesBySemester(
            @PathVariable Long semesterId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            log.info("Getting schedules for semester: {}", semesterId);

            Sort sort = sortDir.equalsIgnoreCase("desc") ?
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            var schedulePage = scheduleService.getSchedulesBySemester(semesterId, pageable);
            var response = PageResponse.of(schedulePage);

            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Get schedules by semester failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lấy lịch giảng thất bại: " + e.getMessage()));
        }
    }

    @GetMapping("/personal")
    @Operation(summary = "Lịch giảng cá nhân", description = "Lấy lịch giảng của giảng viên đang đăng nhập")
    @PreAuthorize("hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<List<ScheduleResponse>>> getPersonalSchedule(
            @RequestParam(required = false) Long semesterId) {
        try {
            log.info("Getting personal schedule for current user, semester: {}", semesterId);
            List<ScheduleResponse> schedules = scheduleService.getPersonalSchedule(semesterId);
            return ResponseEntity.ok(ApiResponse.success(schedules));
        } catch (Exception e) {
            log.error("Get personal schedule failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lấy lịch giảng cá nhân thất bại: " + e.getMessage()));
        }
    }

    @PostMapping("/check-conflict")
    @Operation(summary = "Kiểm tra xung đột", description = "Kiểm tra xung đột lịch giảng trước khi tạo")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<ConflictCheckResponse>> checkScheduleConflict(
            @Valid @RequestBody ScheduleRequest request) {
        try {
            boolean hasConflict = scheduleService.checkScheduleConflict(request);

            ConflictCheckResponse response = new ConflictCheckResponse();
            response.setHasConflict(hasConflict);

            if (hasConflict) {
                response.setMessage("Có xung đột lịch giảng");
                response.setConflictType("SCHEDULE");
            } else {
                response.setMessage("Không có xung đột");
            }

            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Check conflict failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Kiểm tra xung đột thất bại: " + e.getMessage()));
        }
    }

    @GetMapping("/available-rooms")
    @Operation(summary = "Phòng học khả dụng", description = "Lấy danh sách phòng học khả dụng")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<List<AvailableRoomResponse>>> getAvailableRooms(
            @RequestParam Long coSoId,
            @RequestParam String loaiPhong,
            @RequestParam Integer thuHoc,
            @RequestParam Long buoiId,
            @RequestParam Long hocKyId) {
        try {
            log.info("Getting available rooms for coSo: {}, loaiPhong: {}, thu: {}, buoi: {}, hocKy: {}",
                    coSoId, loaiPhong, thuHoc, buoiId, hocKyId);

            List<PhongHoc> rooms = scheduleService.getAvailableRooms(coSoId, loaiPhong, thuHoc, buoiId, hocKyId);

            List<AvailableRoomResponse> response = rooms.stream()
                    .map(room -> new AvailableRoomResponse(
                            room.getIdPhong(),
                            room.getMaPhong(),
                            room.getTenPhong(),
                            room.getLoaiPhong(),
                            room.getSucChua(),
                            room.getCoSo().getTenCoSo(),
                            room.getCoSo().getMaCoSo(),
                            true,
                            "Phòng khả dụng"
                    ))
                    .toList();

            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Get available rooms failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lấy danh sách phòng học thất bại: " + e.getMessage()));
        }
    }

    @GetMapping("/export/personal")
    @Operation(summary = "Xuất lịch cá nhân", description = "Xuất lịch giảng cá nhân ra Excel")
    @PreAuthorize("hasRole('GIANG_VIEN')")
    public ResponseEntity<byte[]> exportPersonalSchedule(@RequestParam(required = false) Long semesterId) {
        try {
            log.info("Exporting personal schedule for current user, semester: {}", semesterId);
            byte[] excelData = scheduleService.exportScheduleToExcel(null, semesterId);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "lich-giang-ca-nhan.xlsx");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelData);
        } catch (Exception e) {
            log.error("Export personal schedule failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/export/teacher/{teacherId}")
    @Operation(summary = "Xuất lịch giảng viên", description = "Xuất lịch giảng của một giảng viên ra Excel")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<byte[]> exportTeacherSchedule(
            @PathVariable Long teacherId,
            @RequestParam(required = false) Long semesterId) {
        try {
            log.info("Exporting schedule for teacher: {}, semester: {}", teacherId, semesterId);
            byte[] excelData = scheduleService.exportScheduleToExcel(teacherId, semesterId);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "lich-giang-giang-vien.xlsx");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelData);
        } catch (Exception e) {
            log.error("Export teacher schedule failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
}
