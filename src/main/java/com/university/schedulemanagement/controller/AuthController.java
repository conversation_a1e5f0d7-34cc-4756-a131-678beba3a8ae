package com.university.schedulemanagement.controller;

import com.university.schedulemanagement.dto.request.LoginRequest;
import com.university.schedulemanagement.dto.request.ChangePasswordRequest;
import com.university.schedulemanagement.dto.response.ApiResponse;
import com.university.schedulemanagement.dto.response.LoginResponse;
import com.university.schedulemanagement.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * AuthController - Controller xử lý xác thực
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Authentication", description = "API xác thực người dùng")
public class AuthController {

    private final AuthService authService;

    @PostMapping("/login")
    @Operation(summary = "Đăng nhập", description = "Đăng nhập vào hệ thống")
    public ResponseEntity<ApiResponse<LoginResponse>> login(@Valid @RequestBody LoginRequest request) {
        try {
            log.info("User login attempt: {}", request.getMaCanBo());
            LoginResponse response = authService.login(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Đăng nhập thành công"));
        } catch (Exception e) {
            log.error("Login failed for user: {}, error: {}", request.getMaCanBo(), e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Đăng nhập thất bại: " + e.getMessage()));
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "Đăng xuất", description = "Đăng xuất khỏi hệ thống")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<String>> logout(@RequestHeader("Authorization") String token) {
        try {
            String jwt = token.startsWith("Bearer ") ? token.substring(7) : token;
            authService.logout(jwt);
            return ResponseEntity.ok(ApiResponse.success("Đăng xuất thành công"));
        } catch (Exception e) {
            log.error("Logout failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Đăng xuất thất bại: " + e.getMessage()));
        }
    }

    @PostMapping("/change-password")
    @Operation(summary = "Đổi mật khẩu", description = "Thay đổi mật khẩu người dùng")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<String>> changePassword(@Valid @RequestBody ChangePasswordRequest request) {
        try {
            authService.changePassword(request);
            return ResponseEntity.ok(ApiResponse.success("Đổi mật khẩu thành công"));
        } catch (Exception e) {
            log.error("Change password failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Đổi mật khẩu thất bại: " + e.getMessage()));
        }
    }

    @PostMapping("/reset-password")
    @Operation(summary = "Reset mật khẩu", description = "Reset mật khẩu cho người dùng")
    public ResponseEntity<ApiResponse<String>> resetPassword(@RequestParam String maCanBo) {
        try {
            authService.resetPassword(maCanBo);
            return ResponseEntity.ok(ApiResponse.success("Reset mật khẩu thành công"));
        } catch (Exception e) {
            log.error("Reset password failed for user: {}, error: {}", maCanBo, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Reset mật khẩu thất bại: " + e.getMessage()));
        }
    }

    @GetMapping("/me")
    @Operation(summary = "Thông tin người dùng", description = "Lấy thông tin người dùng hiện tại")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<Object>> getCurrentUser() {
        try {
            var currentUser = authService.getCurrentUser();
            return ResponseEntity.ok(ApiResponse.success(currentUser));
        } catch (Exception e) {
            log.error("Get current user failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lấy thông tin người dùng thất bại: " + e.getMessage()));
        }
    }
}

