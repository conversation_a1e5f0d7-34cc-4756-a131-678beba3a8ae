package com.university.schedulemanagement.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;
/**
 * ChangePasswordRequest - DTO đổi mật khẩu
 */
@Data
public class ChangePasswordRequest {

    @NotBlank(message = "Mật khẩu cũ không được để trống")
    private String matKhauCu;

    @NotBlank(message = "Mật khẩu mới không được để trống")
    @Size(min = 6, message = "Mật khẩu mới phải có ít nhất 6 ký tự")
    private String matKhauMoi;

    @NotBlank(message = "<PERSON>á<PERSON> nhận mật khẩu không được để trống")
    private String xacNhanMatKhau;
}
