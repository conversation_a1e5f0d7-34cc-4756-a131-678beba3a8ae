package com.university.schedulemanagement.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * ScheduleRequest - DTO tạo/cập nhật lịch giảng
 */
@Data
public class ScheduleRequest {

    // Bước 1: <PERSON><PERSON><PERSON> ngành học, lớ<PERSON>, h<PERSON><PERSON> thức học
    @NotNull(message = "<PERSON><PERSON>i chọn lớp học")
    private Long idLop;

    @NotNull(message = "<PERSON><PERSON>i chọn hình thức học")
    private Long idHinhThuc;

    // Bước 2: <PERSON><PERSON><PERSON><PERSON> thực hành (nếu có)
    private String nhomTh;

    // Bước 3: <PERSON><PERSON><PERSON> mô<PERSON> học, s<PERSON> tiết, hệ số
    @NotNull(message = "<PERSON>ải chọn môn học")
    private Long idMonHoc;

    @NotNull(message = "Số tiết không được để trống")
    @Min(value = 1, message = "<PERSON><PERSON> tiết phải lớn hơn 0")
    private Integer soTiet;

    @DecimalMin(value = "0.0", message = "<PERSON><PERSON> số không được âm")
    @DecimalMax(value = "2.2", message = "<PERSON><PERSON> số không được vượt quá 2.2")
    private BigDecimal heSo = BigDecimal.ONE;

    // Bước 4: Chọn giảng viên
    @NotNull(message = "Phải chọn giảng viên")
    private Long idCanBo;

    // Bước 5: Chọn thứ và buổi học
    @NotNull(message = "Phải chọn thứ học")
    @Min(value = 2, message = "Thứ học từ 2 đến 8")
    @Max(value = 8, message = "Thứ học từ 2 đến 8")
    private Integer thuHoc;

    @NotNull(message = "Phải chọn buổi học")
    private Long idBuoi;

    // Bước 6: Chọn cơ sở, phòng học
    @NotNull(message = "Phải chọn phòng học")
    private Long idPhong;

    // Bước 7: Tuần học và ghi chú
    private String tuanHoc;

    private String ghiChu;

    // Học kỳ
    @NotNull(message = "Phải chọn học kỳ")
    private Long idHocKy;
}