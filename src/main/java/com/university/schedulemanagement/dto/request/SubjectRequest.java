package com.university.schedulemanagement.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * SubjectRequest - DTO tạo/cập nhật môn học
 */
@Data
public class SubjectRequest {

    @NotBlank(message = "M<PERSON> môn học không được để trống")
    private String maMonHoc;

    @NotBlank(message = "Tên môn học không được để trống")
    private String tenMonHoc;

    @NotNull(message = "Phải chọn loại môn học")
    private Long idLoaiMonHoc;

    @NotNull(message = "<PERSON>ải chọn khoa")
    private Long idPbmm;

    @NotNull(message = "<PERSON><PERSON>i chọn hệ đào tạo")
    private Long idHeDaoTao;

    private Long idNhomLk;

    @Min(value = 0, message = "Số tiết lý thuyết không được âm")
    private Integer soTietLt = 0;

    @Min(value = 0, message = "<PERSON><PERSON> tiết thực hành không được âm")
    private Integer soTietTh = 0;

    @Min(value = 0, message = "Số tiết tự học không được âm")
    private Integer soTietTu = 0;

    private Boolean monDieuKien = false;

    private Boolean monTn = false;
}