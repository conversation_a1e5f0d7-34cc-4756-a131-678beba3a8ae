package com.university.schedulemanagement.util;

import com.university.schedulemanagement.entity.LichGiang;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * TeachingHourCalculation - Utility class tính toán giờ giảng
 */
@Slf4j
public class TeachingHourCalculation {

    /**
     * Tính tổng giờ giảng từ danh sách lịch giảng
     */
    public static BigDecimal calculateHours(List<LichGiang> schedules) {
        if (schedules == null || schedules.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalHours = BigDecimal.ZERO;

        for (LichGiang schedule : schedules) {
            if (schedule.getSoTiet() != null && schedule.getHeSo() != null) {
                // Tính giờ = số tiết * hệ số
                BigDecimal hours = BigDecimal.valueOf(schedule.getSoTiet())
                        .multiply(schedule.getHeSo());
                
                totalHours = totalHours.add(hours);
            }
        }

        return totalHours.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Tính giờ giảng cho một lịch giảng cụ thể
     */
    public static BigDecimal calculateHoursForSchedule(LichGiang schedule) {
        if (schedule == null || schedule.getSoTiet() == null || schedule.getHeSo() == null) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(schedule.getSoTiet())
                .multiply(schedule.getHeSo())
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Tính giờ giảng theo loại (lý thuyết/thực hành)
     */
    public static BigDecimal calculateHoursByType(List<LichGiang> schedules, String type) {
        if (schedules == null || schedules.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalHours = BigDecimal.ZERO;

        for (LichGiang schedule : schedules) {
            if (schedule.getSoTiet() != null && schedule.getHeSo() != null) {
                // Kiểm tra loại dựa trên hình thức học hoặc nhóm thực hành
                boolean isMatchingType = false;
                
                if ("LY_THUYET".equals(type)) {
                    // Lý thuyết: không có nhóm thực hành
                    isMatchingType = (schedule.getNhomTh() == null || schedule.getNhomTh().trim().isEmpty());
                } else if ("THUC_HANH".equals(type)) {
                    // Thực hành: có nhóm thực hành
                    isMatchingType = (schedule.getNhomTh() != null && !schedule.getNhomTh().trim().isEmpty());
                }

                if (isMatchingType) {
                    BigDecimal hours = BigDecimal.valueOf(schedule.getSoTiet())
                            .multiply(schedule.getHeSo());
                    totalHours = totalHours.add(hours);
                }
            }
        }

        return totalHours.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Tính giờ giảng theo tháng
     */
    public static BigDecimal calculateHoursByMonth(List<LichGiang> schedules, int month, int year) {
        if (schedules == null || schedules.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalHours = BigDecimal.ZERO;

        for (LichGiang schedule : schedules) {
            if (schedule.getSoTiet() != null && schedule.getHeSo() != null &&
                schedule.getCreatedAt() != null) {
                
                // Kiểm tra tháng và năm
                if (schedule.getCreatedAt().getMonthValue() == month &&
                    schedule.getCreatedAt().getYear() == year) {
                    
                    BigDecimal hours = BigDecimal.valueOf(schedule.getSoTiet())
                            .multiply(schedule.getHeSo());
                    totalHours = totalHours.add(hours);
                }
            }
        }

        return totalHours.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Tính giờ giảng theo học kỳ
     */
    public static BigDecimal calculateHoursBySemester(List<LichGiang> schedules, Long semesterId) {
        if (schedules == null || schedules.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalHours = BigDecimal.ZERO;

        for (LichGiang schedule : schedules) {
            if (schedule.getSoTiet() != null && schedule.getHeSo() != null &&
                schedule.getIdHocKy() != null && schedule.getIdHocKy().equals(semesterId)) {
                
                BigDecimal hours = BigDecimal.valueOf(schedule.getSoTiet())
                        .multiply(schedule.getHeSo());
                totalHours = totalHours.add(hours);
            }
        }

        return totalHours.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Kiểm tra giờ giảng có vượt quá giới hạn không
     */
    public static boolean isOverLimit(BigDecimal hours, BigDecimal limit) {
        if (hours == null || limit == null) {
            return false;
        }
        return hours.compareTo(limit) > 0;
    }

    /**
     * Tính phần trăm hoàn thành giờ giảng
     */
    public static BigDecimal calculateCompletionPercentage(BigDecimal actualHours, BigDecimal targetHours) {
        if (actualHours == null || targetHours == null || targetHours.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return actualHours.multiply(BigDecimal.valueOf(100))
                .divide(targetHours, 2, RoundingMode.HALF_UP);
    }
}
